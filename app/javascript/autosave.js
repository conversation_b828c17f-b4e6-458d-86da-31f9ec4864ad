// ABOUTME: Autosave module for project forms - automatically saves drafts as users type
// ABOUTME: Provides debounced saving, offline support, and visual feedback for form changes

class AutosaveManager {
  constructor(formId, projectId) {
    this.form = document.getElementById(formId);
    this.projectId = projectId;
    this.saveTimeout = null;
    this.isOnline = navigator.onLine;
    this.pendingChanges = false;
    this.lastSaveTime = null;
    this.isSaving = false;
    
    // Simple i18n translations
    this.translations = {
      sk: {
        unsavedChanges: 'Neuložené zmeny',
        saving: 'Ukladá sa...',
        saved: 'Uložené',
        saveFailed: 'Uloženie zlyhalo',
        offline: 'Offline - zmeny sa uložia po obnovení pripojenia',
        confirmLeave: 'Máte neuložené zmeny. Naozaj chcete odísť?'
      },
      en: {
        unsavedChanges: 'Unsaved changes',
        saving: 'Saving...',
        saved: 'Saved',
        saveFailed: 'Save failed',
        offline: 'Offline - changes will be saved when connection is restored',
        confirmLeave: 'You have unsaved changes. Are you sure you want to leave?'
      }
    };
    
    // Get current locale from HTML lang attribute or default to 'sk'
    this.currentLocale = document.documentElement.lang || 'sk';
    
    // Only initialize if we have a form and project ID
    if (this.form && this.projectId) {
      this.initializeEventListeners();
      this.setupOnlineOfflineHandlers();
      this.createSaveIndicator();
    }
  }
  
  // Simple translation helper
  t(key) {
    return this.translations[this.currentLocale]?.[key] || this.translations['en'][key] || key;
  }
  
  initializeEventListeners() {
    // Debounced autosave on input changes
    this.form.addEventListener('input', (e) => {
      if (this.shouldIgnoreField(e.target)) return;
      
      this.pendingChanges = true;
      this.clearSaveTimeout();
      
      // Debounce save for 2 seconds
      this.saveTimeout = setTimeout(() => {
        this.performAutosave();
      }, 2000);
    });
    
    // Also listen for changes (for dropdowns/checkboxes)
    this.form.addEventListener('change', (e) => {
      if (this.shouldIgnoreField(e.target)) return;
      
      this.pendingChanges = true;
      this.clearSaveTimeout();
      
      // Debounce save for 2 seconds
      this.saveTimeout = setTimeout(() => {
        this.performAutosave();
      }, 2000);
    });
    
    // Clear pending changes when form is submitted normally
    this.form.addEventListener('submit', (e) => {
      this.pendingChanges = false;
      this.clearSaveTimeout();
      this.hideSaveIndicator();
    });
    
    // Save before page unload
    window.addEventListener('beforeunload', (e) => {
      if (this.pendingChanges && !this.isSaving) {
        // Attempt synchronous save
        this.performAutosave(true);
        e.preventDefault();
        e.returnValue = this.t('confirmLeave');
      }
    });
  }
  
  shouldIgnoreField(element) {
    // Don't autosave file inputs, publish button, or system fields
    return element.type === 'file' || 
           element.name === 'authenticity_token' ||
           element.name === 'publish' ||
           element.name === 'draft' ||
           element.classList.contains('no-autosave');
  }
  
  async performAutosave(synchronous = false) {
    if (!this.isOnline || !this.pendingChanges || this.isSaving) {
      return;
    }
    
    this.isSaving = true;
    const formData = new FormData(this.form);
    
    // Ensure we're saving as draft for autosave
    formData.set('draft', 'true');
    formData.append('autosave', 'true');
    
    try {
      const response = await fetch(`/projects/${this.projectId}`, {
        method: 'PATCH',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      });
      
      if (response.ok) {
        this.pendingChanges = false;
        this.lastSaveTime = new Date();
      } else {
        this.showErrorIndicator();
      }
    } catch (error) {
      console.error('Autosave failed:', error);
      this.showErrorIndicator();
    } finally {
      this.isSaving = false;
    }
  }
  
  setupOnlineOfflineHandlers() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.hideSaveIndicator();
      if (this.pendingChanges) {
        this.performAutosave();
      }
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineIndicator();
    });
  }
  
  clearSaveTimeout() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
  }
  
  showErrorIndicator() {
    this.updateSaveIndicator(this.t('saveFailed'), 'error');
  }
  
  showOfflineIndicator() {
    this.updateSaveIndicator(this.t('offline'), 'offline');
  }
  
  updateSaveIndicator(text, status) {
    const indicator = document.getElementById('autosave-indicator');
    if (indicator) {
      indicator.textContent = text;
      indicator.className = `autosave-indicator ${status}`;
      indicator.style.display = 'block';
    }
  }
  
  hideSaveIndicator() {
    const indicator = document.getElementById('autosave-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }
  
  createSaveIndicator() {
    // Check if indicator already exists
    if (document.getElementById('autosave-indicator')) {
      return;
    }
    
    const indicator = document.createElement('div');
    indicator.id = 'autosave-indicator';
    indicator.className = 'autosave-indicator';
    indicator.style.display = 'none';
    
    // Insert into sharing sidebar if available, otherwise into form
    const container = document.querySelector('.sharing-sidebar') || this.form;
    if (container) {
      container.appendChild(indicator);
    }
  }
  
  // Public method to manually save
  forceSave() {
    if (this.pendingChanges) {
      this.clearSaveTimeout();
      this.performAutosave();
    }
  }
  
  // Public method to check if there are unsaved changes
  hasUnsavedChanges() {
    return this.pendingChanges;
  }
}

// Initialize autosave for project forms
document.addEventListener('DOMContentLoaded', () => {
  const projectForm = document.getElementById('project_form');
  if (projectForm) {
    const projectId = projectForm.dataset.projectId;
    if (projectId) {
      window.autosaveManager = new AutosaveManager('project_form', projectId);
    }
  }
});

// Export for potential use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AutosaveManager;
}