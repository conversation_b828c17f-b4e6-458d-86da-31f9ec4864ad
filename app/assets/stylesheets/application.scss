@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@import "variables";
@import "connections_cards";
@import "uploads";
@import "project_form";

// Utility classes
.hidden {
  display: none !important;
}

// Reset & Base
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
 
body {
  font-family: "Inter", sans-serif;
  font-size: $base-font-size;
  line-height: 1;
  color: $text-color;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  &.welcome {
    margin: 0;
    // background-color: $secondary-color;
    // color: white;
    // text-shadow: 0 0 10px rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    h1 {
      font-size: 3rem;
      margin-bottom: 20px;
    }
    p {
      font-size: 1.5rem;
      margin-bottom: 20px;
    }
  }
}

a {
  color: $blue;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
}
 
// New Layout Structure
.app-layout,
.app-layout-with-sidebar {
  display: flex;
  min-height: 100vh;
  background-color: $gray;
}

// Main content takes remaining space after sidebar
main {
  flex: 1;
}

.main-welcome  {
  flex: 0 1;
}

// Sidebar Styles
.sidebar {
  width: 280px;
  background-color: $secondary-color;
  color: white;
  padding: 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: white;
  
  &:visited {
    color: white;
  }
}

.sidebar-logo {
  height: 40px;
  width: auto;
}

.sidebar-nav {
  padding: 20px 0;
  flex: 1;
}

.nav-section {
  margin-bottom: 30px;
}

.nav-section-header {
  padding: 8px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 500;
  color: white;
  
  &.admin-header {
    font-size: 18px;
    font-weight: 500;
  }
}

.nav-section-items {
  padding-left: 10px;
}

.nav-item {
  display: block;
  padding: 4px 20px;
  font-size: 16px;
  color: #CCCCCC;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;
  
  &:visited {
    color: #CCCCCC;
  }
  
  &:hover {
    color: #FFFFFF;
    text-decoration: none;
  }
  
  &.active {
    color: #FFFFFF;
    font-weight: bold;
  }
  
  &.logout-btn {
    background: none;
    border: none;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
  }
}

// Main Content
main {
  background-color: $gray;
  display: flex;
  flex-direction: column;
}

.main-header {
  background-color: $secondary-color;
  padding: 16px 32px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 24px;
  position: relative; 
}

// Original working navbar circle and dropdown
.navbar__circle {
  width: 40px;
  height: 40px;
  background-color: $primary-color;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.navbar__initials {
  font-size: 20px;
  font-weight: 500;
  color: $secondary-color;
}

.navbar__menu {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px; /* Added for spacing */
  background: white;
  border: 1px solid $border-color;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  z-index: 1000;
  padding: $spacing-unit;
  list-style: none;
  margin: 0;
  
  &--hidden {
    display: none;
  }
  
  &.active {
    display: block;
  }
  
  li {
    list-style: none;
    margin: 0;
  }
  
  a, button {
    display: block;
    padding: $spacing-unit * 0.5;
    color: $text-color;
    text-decoration: none;
    width: 100%;
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }
}

.content-container {
  padding: 8px 32px;
  flex: 1;
}

// Public header for non-logged users
.public-header {
  background-color: $secondary-color;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .public-nav {
    display: flex;
    gap: 16px;
    align-items: center;
  }
}

// Mobile menu
.mobile-menu {
  display: none !important;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  flex-direction: column;
  background: white;
  padding: $spacing-unit;
  border-bottom: 1px solid $border-color;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  
  @media (max-width: $breakpoint-mobile) {
    display: none; // Show only when explicitly activated on mobile
  }
  
  &.active {
    display: flex;
  }
  
  li {
    list-style: none;
    margin: 0;
  }
  
  a {
    display: block;
    padding: $spacing-unit;
    color: $text-color;
    text-decoration: none;
    
    &:hover {
      color: $primary-color;
    }
  }
}

// Legacy sidebar menu styles - keeping for backward compatibility
.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    margin-bottom: 2px;
  }
  
  a {
    display: block;
    padding: $spacing-unit $spacing-unit ;
    color: white;
    text-decoration: none;
    border-left: 4px solid transparent;
    &:visited {
      color: white;
    }

    &.active {
      background-color: $primary-color;
      color: white;
    }
  }
}

// Legacy mobile styles - keeping for backward compatibility
@media (max-width: $breakpoint-mobile) {
  .sidebar.legacy {
    position: fixed;
    top: 60px; 
    left: -450px; 
    height: calc(100vh - 60px);
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: none;
    
    &-mobile-visible {
      left: 0;
      box-shadow: 2px 0 5px rgba(0,0,0,0.2);
    }
  }
  
  .main-content-wrapper {
    margin-left: 0;
    width: 100%;
  }
}

.sub-menu {
  list-style: none;
  padding: 0;
  margin: 0 0 1.25rem 0;
  
  li {
    margin-bottom: 2px;
  }
  
  a, .text-link {
    display: block;
    padding: $spacing-unit * 0.75 $spacing-unit; 
    color: white;
    text-decoration: none;
    border-left: 4px solid transparent;
    
    &:visited {
      text-decoration: none;
      color: white;
    }
    
    &.active {
      text-decoration: none;
      background-color: $secondary-color;
      color: white;
      font-weight: 600;
    }
  }
}

.logout-link {
  font-size: inherit;
  color: inherit;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: block;
  padding: $spacing-unit * 0.75 $spacing-unit; 
  border-left: 4px solid transparent;
}




// Legacy - keeping for backward compatibility
.main-content-wrapper {
  flex-grow: 1;
}

@media (max-width: $breakpoint-mobile) {
  .app-layout {
    flex-direction: column;
  }
  
  .sidebar {
    //width: 100%;
    display: flex;
    flex-direction: column; 
    justify-content: space-between;
    margin-right: 0;
    margin-bottom: $spacing-unit;
    border-right: none;
    padding: $spacing-unit * 0.5 0;
  }
  
  .sidebar-menu {
    display: flex;
    flex-wrap: wrap;
    
    li {
      margin-bottom: 0;
      width: 100%;
    }
    
    a {
      padding: $spacing-unit * 0.5 $spacing-unit * 0.75;
    }
  }
}


// Legacy main styles
main.legacy {
  display: flex;
  justify-content: center;
}
 

a {
  color: $link-color;
}

a:visited {
  color: $link-color;
}

h1, h2, h3 {
  width: 100%;
  margin-bottom: $spacing-unit;
  color: $secondary-color;
  text-align: left;
}


 .logo {
  width: 200px;
  height: auto;
}

.tagline {
  margin-top: 20px;
  font-size: 1.5rem;
  max-width: 80vw;
  font-weight: 600;
}

.subtagline {
  margin-top: 10px;
  font-size: 1.2rem;
  max-width: 80vw;
}


//Tags
.tag {
  display: inline-block;
  padding: $spacing-unit * 0.25 $spacing-unit * 0.75;
  //border-radius: $border-radius;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  gap: 10px;
  margin: 5px 5px;

  &.info {
    background-color: $info-color;
    color: white;
  }

  &.inactive {
    background-color: $inactive-color;
    color: #a7a7a7;
  }

  &.attention {
    background-color: $attention-color;
    color: white;
  }
  &.false {
    background-color: $brightcoral;
    color: white;
  }
  &.true {
    background-color: $ready-color;
    color: white;
  }
  &.ready {
    background-color: $ready-color;
    color: white;
  }
  &.mynetwork {
    background-color: $brown;
    color: white;
  }
  &.mycontact {
    background-color: $blue;
    color: white;
  }
  &.full_details {
    background-color: $green;
    color: $info-color
  }
  &.pending {
    background-color: $secondary-color;
    color: white;
  }
  &.denied {
    background-color: $brightcoral;
    color: white;
  }
}

@media (min-width: 768px) {
  .tagline {
      max-width: 50vw;
  }
}


.button_to, button {
  display: inline-block;
  font-family: $base-font;
}

.text-link {
  font-size: $base-font-size;
  color: $link-color;
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

// Layout
.container {
  width:100%; 
  max-width: 1024px;
  padding-top: $spacing-unit;
  //margin: 10px auto;
  display: flex;
  flex-direction: column;
}

// New Project Details Design
.project-title-box {
  background-color: white;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
  
  .title-header-bar {
    height: 4px;
    background-color: $primary-color;
  }
  
  .title-content {
    padding: 24px 32px;
    
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: $title-color;
    }
    
    .location-text {
      font-size: 16px;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .icon-16 {
        width: 16px;
        height: 16px;
      }
    }
  }
}

// Content grid container
.project-content-grid {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  align-items: flex-start;
}

// Separate left and right content boxes
.project-content-left-box {
  background-color: white;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  padding: 32px;
  margin-bottom: 24px;
  flex: 1;
  
  .section-title {
    font-weight: 600;
    margin-bottom: 16px;
    text-transform: uppercase;
    color: $subtitle-color;
  }
  
  .description-text {
    line-height: 1.15;
    color: $text-color;
    margin-bottom: 20px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    p {
      margin-bottom: 5px;
    }
  }
  
  .detail-label {
    color: $subtitle-color;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 5px;
  }
  .project-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    
    .detail-item {
      
      .detail-value {
        color: $text-color;
      }
    }
  }
}

.project-content-right-box {
  background-color: white;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  padding: 32px;
  margin-bottom: 24px;
  width: 400px;
  
  .price-display {
    font-size: 40px;
    font-weight: 600;
    color: $green;
    margin-bottom: 24px;
    
    .price-unit {
      font-size: 16px;
      font-weight: 500;
      color: $text-color;
    }
  }
  
  .info-rows {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #555555;
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 0.1px;
      padding-bottom: 10px;
      margin-bottom: 16px;
      border-bottom: 1px solid #E5E5E5;
      
      .info-label {
      }
      
      .info-value {
      }
    }
  }
  
  .action-button {
    width: 100%;
    padding: 14px;
    background-color: $blue;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    display: inline-block;
    
    &:hover {
      text-decoration: none;
    }    

    &:visited {
      color: white;
    }
  }
}

// Restore content-right within content-grid as in React template

.project-attachments-box {
  background-color: white;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  padding: 32px;
  
  h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: $title-color;
  }

  .flex.g1.mb-4 {
    @media (min-width: 769px) { // Desktop
      flex-wrap: nowrap;

      .files-box {
        flex: 1;
        min-width: 0; /* Prevents content from overflowing */
      }
      .side-right {
        flex: 0 0 200px;
      }
    }

    @media (max-width: 768px) { // Mobile
      flex-direction: column;

      .files-box {
        order: 2;
        width: 100%;
      }
      .side-right {
        order: 1;
        width: 100%;
      }
    }
  }
}

// Update file grid for new design
.file-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 1rem;
  
  .file-item {
    width: 200px;
    height: 200px;
    text-align: center;
    
    // Mobile responsive
    @media (max-width: 768px) {
      width: 100%;
      height: auto;
    }
    
    .file-thumbnail-container {
      width: 200px;
      height: 140px;
      background-color: #e8e8e8;
      border-radius: 8px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      
      // Mobile responsive
      @media (max-width: 768px) {
        width: 100% !important;
        height: 100px;
      }
    }
    
    .file-name {
      font-weight: 500;
      font-size: $small-font-size;
      margin-bottom: 2px;
      color: $text-color;
    }
    
    .file-meta {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .download-link {
      padding: 6px 16px;
      background-color: transparent;
      color: $blue;
      border: 1px solid $blue;
      border-radius: 4px;
      font-size: $small-font-size;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      text-decoration: none;
      
      &:hover {
        background-color: rgba($blue, 0.1);
        text-decoration: none;
      }
    }
    .file-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 8px;
      // Always cropped, never scaled inside
    }
  }
}

// Mobile Responsive Styles
@media (max-width: $breakpoint-mobile) {
  .app-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 80%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
    
    &.mobile-visible {
      left: 0;
    }
  }
  
  main{
    width: 100%;
    margin-left: 0;
  }
  
  .main-header {
    padding: 12px 16px;
    
    .header-actions {
      gap: 16px;
    }
  }
  
  .content-container {
    padding: 16px;
  }
  
  .nav-section-header {
    font-size: 16px;
    padding: 10px 16px;
  }
  
  .nav-section-items {
    padding-left: 40px;
  }
  
  .nav-item {
    padding: 6px 0;
  }
  
  // Project details mobile styles
  .project-content-grid {
    flex-direction: column;
    
    .project-content-right-box {
      width: 100%;
    }
  }
  
  .project-content-box .content-grid {
    flex-direction: column;
    gap: 24px;
    
    .content-right {
      width: 100%;
    }
  }
  
  .project-details-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }
  
  .file-grid {
    grid-template-columns: 1fr !important;
  }
  
  .project-title-box .title-content {
    padding: 16px 20px;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .project-content-box {
    padding: 20px;
  }
  
  .project-attachments-box {
    padding: 20px;
  }
  
  // Mobile button adjustments
  .button,
  .button-outline,
  input[type="submit"] {
    width: 100%;
    padding: 12px 24px;
    font-size: 16px;
  }
  
  // Mobile form adjustments
  form {
    .form-card {
      padding: 16px;
      margin: 8px 0;
    }
  }
  
  // Mobile header toggle
  .user-avatar {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 2px;
      background: white;
      box-shadow: 0 6px 0 white, 0 -6px 0 white;
    }
    
    // Hide initials on mobile and show hamburger
    font-size: 0;
  }
  
  .mobile-menu {
    &.active {
      display: flex;
      height: 100vh;
      z-index: 1001;
    }
  }
}

@media (max-width: $breakpoint-mobile) {
  .container {
    padding: 5px;
    margin: 5px auto;
  }
}

.main-box {
  width: 100%;
  display: flex;
  flex: 1;
  text-align: left;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
 
.flex {
  display: flex;
  &-center {
    justify-content: center;
    align-items: center;
  }
  &-between {
    justify-content: space-between;
  }
  &-column {
    flex-direction: column;
  }
}

//Tabs menu
.tab-menu {
  display: flex;
  border-bottom: 1px solid #ccc;
  gap: 2px;
}

.tab-menu a {
  padding: 10px 20px;
  text-decoration: none;
  color: #5e5d5d;
  border: 1px solid #ccc;
  border-bottom: none;
  background-color: $gray;
  align-items: center; 
}

.tab-menu a.active {
  background-color: #CCCCCC;
  font-weight: bold;
  color: $primary-color;
}

.tab-menu a:hover {
  background-color: #ddd;
}

@media (max-width: $breakpoint-mobile) {
  .tab-menu a {
    padding: 10px 17px;
  }
}
 
// Navigation
.navbar {
  background: #3D3F40;
  color: white;
  padding: 0.5rem 0.5rem;
  border-bottom: 1px solid #3D3F40;
  position: relative;
  
  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__brand {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    color: $primary-color;
  }

  &__image {
    height: 43px;
    object-fit: contain;
  }
  
  &__menu {
    display: flex;
    color: $secondary-color;
    font-weight: 500;
    gap: $spacing-unit;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    margin-left: auto; // Aligns the menu to the right
    
    @media (max-width: $breakpoint-mobile) {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      flex-direction: column;
      background: white;
      padding: $spacing-unit;
      border-bottom: 1px solid $border-color;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      
      &.active {
        display: flex;
        height:100vh;
        z-index: 1000;
      }
    }
    
    a {
      text-decoration: none;
      color: $text-color;
      padding: $spacing-unit * 0.5;
      
      &:hover {
        color: $primary-color;
      }
      
      @media (max-width: $breakpoint-mobile) {
        display: block;
        padding: $spacing-unit;
      }
    }
  }
  
  &__circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    color: $primary-color;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin: 0 0.5rem;

    @media (max-width: 768px) {
      background-color: #3D3F40 !important;
      .navbar__initials {
        display: none;
      }

      &::before {
        content: '';
        display: block;
        width: 24px;
        height: 2px;
        background: white;
        position: relative;
        box-shadow: 0 8px 0 white, 0 -8px 0 white;
      }
    }
  }

  &__initials {
    color: white;
    font-size: 1rem;
    font-weight: bold;

    @media (max-width: 768px) {
      display: none;
    }
  }
}

.navbar__menu--hidden {
  display: none;
}

#navbarToggle {
  cursor: pointer;
}

#navbarMenu.navbar__menu--hidden {
  display: none;
}

#navbarMenu {
  display: flex;
  flex-direction: column;
  gap: $spacing-unit;
  min-width: 300px;
  list-style: none;
  margin: 0;
  padding: 30px;
  align-items: center;
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid $border-color;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index:10;
}

.navbar__circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: white;
  background-color: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;

  @media (min-width: $breakpoint-mobile) {
    &.active {
      background-color: $secondary-color;
      transform: rotate(360deg);
    }
  }
}

.navbar__initials {
  color: #3D3F40;
  font-size: 1.25em;
  font-weight: bold;
  transition: color 0.3s ease;

  .navbar__circle.active & {
    color: $primary-color;
  }
}

.mobile-only {
  display: none;
}

@media (max-width: $breakpoint-mobile) {
  .mobile-only {
    display: block;
  }
}

.side-right {
  flex: 0 1 19%;
  order: 2;
  .side-box {
    //background-color: $light-gray;
    border: 1px solid $border-color;
    padding: $spacing-unit;
    margin-bottom: $spacing-unit;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    line-height: 1.4em;
  }
}

main {
}

.main-content {
  flex: 0 1 70%;
  order: 1;
}

@media (max-width: 768px) {
  .side-right {
    order: 1;
    flex: 1 1 100%;
  }
  .main-content {
    order: 2;
    flex: 1 1 100%;
  }
}

.large-form {
  width: 100%;
  
  // Refactor this solution for all the forms if it will work for the project form and profile forms. 
  // Prevent iOS form zoom
  -webkit-text-size-adjust: 100%;
  
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea,
  select {
    // Prevent iOS zoom on focus
    font-size: 16px;
    // Improve iOS appearance
    -webkit-appearance: none;
    appearance: none;
    // Ensure proper border radius on iOS
    border-radius: 0.375rem;
    // Prevent iOS shadow artifacts
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    // Enable smooth scrolling on iOS
    -webkit-overflow-scrolling: touch;
    // Prevent iOS text size adjustment
    -webkit-text-size-adjust: 100%;
    
    &:focus {
      // Improve iOS focus state
      outline: none;
      -webkit-box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
    }
  }

  // Specific select styling for iOS
  select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1em;
    padding-right: 2.5rem;
    
    // Fix for iOS select padding
    text-indent: 1px;
    text-overflow: '';
  }

  // Improve file input on iOS
  input[type="file"] {
    // Prevent iOS zoom
    font-size: 16px;
    // Fix iOS file input display
    &::-webkit-file-upload-button {
      -webkit-appearance: button;
      font: inherit;
      font-size: 16px;
      padding: 0.5em 1em;
      border: 1px solid $border-color;
      border-radius: 0.375rem;
      background: white;
    }
  }

  // Fix iOS checkbox appearance
  input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    width: 1.2em;
    height: 1.2em;
    border: 1px solid $border-color;
    border-radius: 0.25em;
    margin-right: 0.5em;
    position: relative;
    
    &:checked {
      background-color: $primary-color;
      border-color: $primary-color;
      
      &:after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 6px;
        height: 11px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
    }
  }

  // Fix iOS textarea scrolling
  textarea {
    -webkit-overflow-scrolling: touch;
    // Prevent iOS bounce effect
    overscroll-behavior: none;
  }

  // Fix horizontal overflow on mobile for row layouts
  @media (max-width: $breakpoint-mobile) {
    .row {
      flex-direction: column;
      width: 100%;
      
      .flex-inline {
        width: 100%;
        margin-bottom: 0.5rem;
      }
    }
    
    // Prevent horizontal overflow by constraining form element widths
    input[type="text"],
    input[type="email"], 
    input[type="password"],
    textarea,
    select {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
    }
    
    // Ensure the form itself doesn't overflow
    form {
      max-width: 100%;
      overflow-x: hidden;
    }
  }
}

// Fix for iOS modal scrolling
.modal {
  -webkit-overflow-scrolling: touch;
  // Prevent iOS bounce effect
  overscroll-behavior: none;
}

// Updated Form Styles
form {
  .form-card {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 24px;
    margin-top: $spacing-unit;
    margin-bottom: $spacing-unit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  label {
    display: inline-block;
    color: $text-color;
    font-weight: 600;
    margin-top: $spacing-unit * 0.25;
    margin-bottom: $spacing-unit * 0.5;
  }
  
  input[type="checkbox"] {
    margin-right: 15px;
  }
  
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    height: 44px;
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    font-size: 16px;
    font-family: $base-font;
    border-radius: 8px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: white;

    &:focus {
      outline: none;
      border-color: $blue;
      box-shadow: 0 0 0 3px rgba($blue, 0.1);
    }
    
    &.title-input {
      font-size: 24px;
      font-weight: 600;
      height: 60px;
    }
  }
  
  textarea {
    min-height: 120px;
    height: auto;
    resize: vertical;
  }
  
  select {
    height: 44px;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: white;
    font-size: 16px;
    color: $text-color;
    font-family: $base-font;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
    &:focus {
      border-color: $blue;
      outline: none;
      box-shadow: 0 0 0 3px rgba($blue, 0.1);
    }
  }
}

.profile-edit {
  max-width: 500px;
  -webkit-overflow-scrolling: touch;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: $spacing-unit;
    margin-bottom: $spacing-unit;
    // To prevent iOS Safari flexbox issues
    width: 100%;
  }

  .form-field-group {
    display: flex;
    flex-direction: column;
    // To prevent text zoom issues on iOS
    -webkit-text-size-adjust: 100%;

    @media (min-width: ($breakpoint-mobile)) {
      flex-basis: calc(50% - (#{$spacing-unit} / 2));
      min-width: 0;
      // Ensure proper sizing on iOS
      width: calc(50% - (#{$spacing-unit} / 2));
    }

    @media (max-width: $breakpoint-mobile) {
      flex-basis: 80%;
      width: 80%;
      // Safe area inset for notched iOS devices
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }

    input[type="text"],
    input[type="email"],
    input[type="password"] {
      width: 100%;
      // Prevent iOS zoom on input focus
      font-size: 16px;
    }
  }

  .form-field-full-width {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    margin-bottom: $spacing-unit;
    
    textarea {
      width: 100%;
      // Prevent iOS zoom on textarea focus
      font-size: 16px;
    }
    
    @media (max-width: $breakpoint-mobile) {
      flex-basis: 80%;
      width: 80%;
      // Safe area inset for notched iOS devices
      padding-left: env(safe-area-inset-left);
      padding-right: env(safe-area-inset-right);
    }
  }

  label {
    width: 150px;
  }
}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  line-height: 1.4em;
}

.checkbox input[type="checkbox"] {
  margin-right: 10px;
}

.field {
  max-width: 500px;
  margin: $spacing-unit auto;
  label {
    display: block;
    margin-bottom: $spacing-unit * 0.5;
    font-weight: 500;
  }

  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    width: 100%;
    padding: $spacing-unit * 0.5;
    border: 1px solid $border-color;
    font-size: $small-font-size;
    border-radius: 0.375rem;
    
    &:focus {
      outline: none;
      border-color: $primary-color;
    }
  }
}


// Standardize form inputs
.form-input,
.form-select {
  height: 44px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  font-size: 16px;
  line-height: 1.25;
  background-color: white;
  border-radius: 8px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: $blue;
    box-shadow: 0 0 0 3px rgba($blue, 0.1);
  }
}

.form-select {
  padding-right: 40px;
  // background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  cursor: pointer;
}


input:invalid, select:invalid, textarea:invalid {
  border: 1px solid $red !important;
  background-color: #fff8f8;
}

input:focus:invalid {
  box-shadow: 0 0 5px $red;
}

.file-error {
  color: $brightcoral;
}

.file-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  .file-name {
    max-width: 700px;
    display:inline-block;
    white-space: normal;
    overflow-wrap: break-word;
    a {
      display: inline-block; 
      width: 100%;          
      white-space: normal;  
      overflow-wrap: break-word; 
      word-break: break-word;    
    }
  }
  .file-meta {
    color: #aaa;
  }
}

.file-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.downloadButton,
.view-button {
  font-size: $base-font-size;
  color: $link-color;
  text-decoration: underline;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.download-icon,
.view-icon {
  pointer-events: none;
}

.file-box {
  position: relative;
}

.download-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($secondary-color, 0.95);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 9999;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  text-align: center;
  z-index: 1000;
  padding: 10px;
  box-sizing: border-box;
}

.loading-dots::after {
  content: '.';
  animation: dots 1s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(255,255,255,0);
    text-shadow:
      .25em 0 0 rgba(255,255,255,0),
      .5em 0 0 rgba(255,255,255,0);
  }
  40% {
    color: white;
    text-shadow:
      .25em 0 0 rgba(255,255,255,0),
      .5em 0 0 rgba(255,255,255,0);
  }
  60% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 rgba(255,255,255,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 white;
  }
}



// Filter layout
.filter-form {
  width: 100%;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: $spacing-unit;
}

.filter-row {
  display: flex;
  gap: $spacing-unit;
  align-items: center;
  flex-wrap:nowrap;
  
  &:first-child {
    flex: 1;
    justify-content: flex-start;
  }
  
  &:last-child {
    justify-content: space-between;
  }
}

.filter-group {
  display: flex;
  gap: $spacing-unit * 0.5;
  align-items: center;
  
  select {
    width: 172px;
  }
}

.location-group {
  .form-input {
    width: 140px;
  }
}

// New toggle group style
.toggle-group {
  display: flex;
  gap: 2px;
  padding: 2px;
  border-radius: $border-radius;
}

.toggle-input {
  display: none;
  
  &:checked + .toggle-label {
    color: #3D3F40;
    opacity: 0.5;
    background-color: #F5F5F5;
    border-color: #F5F5F5;
  }
}

.toggle-label {
  height: 44px;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  margin: 0;
  font-size: 16px;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  
  &:hover {
    background-color: rgba(255,255,255,0.5);
  }
}

.filter-actions {
  display: flex;
  gap: $spacing-unit;
  align-items: center;
}

// Mobile responsiveness
@media (max-width: $breakpoint-mobile) {
  .filter-row {
    flex-direction: column;
    width: 100%;
    flex-wrap:wrap;
    
    &:last-child {
      flex-direction: column;
      align-items: stretch;
    }
  }
  
  .filter-group {
    width: 100%;
    flex-wrap: wrap;
    
    .form-input,
    .form-select {
      flex: 1;
      width: auto;
    }
  }
  
  .location-group {
    .form-input {
      flex: 2;
    }
    .form-select--small {
      flex: 1;
    }
  }
  
  .toggle-group {
    width: 100%;
    justify-content: center;
  }
  
  .filter-actions {
    flex-direction: column;
    width: 100%;
    
    .button {
      width: 100%;
    }
  }
}


 
// Updated Button Styles
.button {
  display: inline-block;
  padding: 14px 24px;
  border: none;
  background-color: $blue;
  color: white;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: center;

  &a:visited {
    color: white;
  }

  &:visited {
    color: white;
  }
  
  &:hover {
    background-color: darken($blue, 10%);
    text-decoration: none;
  }
  
  &-primary {
    background-color: $primary-color;
    
    &:hover {
      background-color: darken($primary-color, 10%);
    }
  }
  
  &-secondary {
    background-color: $secondary-color;
    
    &:hover {
      background-color: darken($secondary-color, 10%);
    }
  }
  
  &-outline {
    background-color: transparent;
    border: 1px solid $blue;
    color: $blue;
    
    &:hover {
      background-color: rgba($blue, 0.1);
      color: $blue;
    }
    
    &:visited {
      color: $blue;
    }
  }
}


input[type="file"] {
  display: inline-block;
}

.button-outline {
  display: inline-block;
  padding: 14px 24px;
  border: 1px solid $blue;
  color: $blue;
  font-weight: 500;
  text-decoration: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  
  &:hover {
    background-color: rgba($blue, 0.1);
    text-decoration: none;
    color: $blue;
  }
  
  &:visited {
    color: $blue;
  }
}

.button-prominent {
  display: inline-block;
  padding: 0.6rem;
  border: none;
  background-color: $blue;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  min-width: 70px;
  font-weight: 500;
  font-family: $base-font;

  cursor: pointer;
  &:hover {
    text-decoration: none;
  }
  &:visited {
    color: white;
  }
 }

input[type="submit"] {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  background: $blue;
  color: white;
  font-family: $base-font;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:visited {
    color: white;
  }
  
  &:hover {
    text-decoration: none;
  }
}

.actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-top: $spacing-unit;
  gap: 10px;
}
 
.explanation {
  font-size: 0.9em;
  margin-top: 5px;
  &.info {
  color: #666;
  };
  &.alert {
    color: $brightcoral;
  }
}

#error_explanation {
   background-color: lighten($brightcoral, 40%);
   border: 1px solid lighten($brightcoral, 20%);
   border-radius: 5px;
   color: $brightcoral;
   margin: 20px 0;
   padding: 10px;
   text-align: left;
   ul {
     list-style: none;
   }
 }
 
// Cards
.cards-container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  gap: $spacing-unit;
}
.card {
  width: 380px;
  min-height: 450px;
  background: white;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: $spacing-unit;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: $spacing-unit;

  .card-upper-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .card-profile {
    display: flex;
    align-items: center;
    margin: $spacing-unit/2 0;

    .info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: calc(100% - 80px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 5px;

      .name {
        font-size: 18px;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 8px;
      }

      .data {
        color: #666;
        margin-bottom: 4px;
      }
    }
  }

  .card-text {
    margin: $spacing-unit 0;
    margin-left: 10px;
    p {
      margin: 0;
    }
  }

  .card-message {
    flex: 1;
    margin: $spacing-unit 0;
    p {
      margin: 0;
    }
  }

  .card-lower-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: $info-color;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 20px;
}



.profile-tag {
  width: 260px;
  height: 80px;
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 10px;
  margin: 5px;
  box-sizing: border-box;
}
.profile-tag .circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: $blue;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  font-weight: bold;
  margin-right: 20px;
}
.profile-tag .info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: calc(100% - 80px); /* Adjust width to fit within the parent */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: wrap;
}
.profile-tag .info .name {
  font-size: 18px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.profile-tag .info .location {
  color: #666;
}


 
// Lists
.list {
  display: flex;
  flex: 100%;
  flex-direction: column;
  justify-content:baseline;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  margin: 0.25em;
} 

.list-box {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  min-width: 150px;
  padding: 20px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  margin-bottom: 15px;
  border: 1px solid #ddd;
  background-color: #fff;
}

.list-item {
  width: 100%;
  display: flex;
  flex: 1; 
  text-align: left;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.show-box {
  max-width: 1024px;
  margin: 10px auto;
  padding: 5px;
  display: flex;
  flex-direction: column;
}

//File list
.file-list {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 10px;
  gap: 10px;
  margin: $spacing-unit / 2 0;
}

.file-list-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px;
  gap: 5px;
  white-space: normal;
  &:hover {
    background-color: $light-gray
  }
}

.file-link {
  color: $link-color;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
}

.delete-button {
  @extend .button;
  background-color: $attention-color;
  color: white;
  padding: 5px; 
  font-weight: 500;
  &:hover {
    opacity: 0.7;
  }
}


@media (max-width: 768px) {
  .file-list-item {
    padding: 10px 5px;
  }
  .file-info {
    display: flex;
    width: 100%;
    min-width: 0;
    .file-name {
      max-width: 300px;
      display:inline-block;
      white-space: normal;
      overflow-wrap: break-word;
      a {
        display: inline-block;
        width: 100%;
        white-space: normal;
        overflow-wrap: break-word;
        word-break: break-word;
      }
    }
  }
}
 
//Modal
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
  
}

// .modal-content {
//   background-color: #fefefe;
//   margin: 25% auto;
//   padding: 20px;
//   width: 450px;
//   position: relative;
// }

.close-button {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close-button:hover,
.close-button:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.icon-16 {
  width: 16px; 
  height: 16px;
}
.icon-20-inline {
  display: inline;
  width: 20px; 
  height: 20px;
}
.icon-20 {
  width: 20px; 
  height: 20px;
}
.icon-24 {
  width: 24px; 
  height: 24px;
}

.notification {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  margin: 0 0.5rem;
  a {
    color: white;
  }
}

@media (max-width: $breakpoint-mobile) {
  .notification {
    display: flex !important; 
  }
}

.count-badge {
  min-width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: 11px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  text-align: center;
  line-height: 1.75;
  &.navbar {
    position: absolute;
    top: 2px;
    right: -5px;
    display: none;
    background-color: $red;
    color: white;
  }
  &.info {
    background-color: $red;
    color: white;
    display: inline;
  }
  &.inline {
    background-color: $red;
    color: white;
  }
}

.mini-badge-box {
  position: relative;
  display: inline-block;
}

.mini-badge {
  position: absolute;
  top: 0;
  right: -8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #FF4D4F;
  display: block;
}


.translations {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  .language-selector {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 15px;
  }
  &.mobile {
    display: none;
  }
}

@media (max-width: $breakpoint-mobile) {
  .translations {
    &.mobile {
      display: flex;
    }
  }
}



// Utilities
.mt-1 { margin-top: $spacing-unit; }
.mb-1 { margin-bottom: $spacing-unit; }
.mb-2 { margin-bottom: $spacing-unit*2; }
.mb-4 { margin-bottom: $spacing-unit*4; }
.mr-2 { margin-right: 0.25rem;}
.p-1 { padding: $spacing-unit; }
.w-1 {width: 100%;}
.g1 { gap: $spacing-unit}
.g2 { gap: $spacing-unit*2}
.g4 { gap: $spacing-unit*4}

.text-r {text-align: right;}
.text-l {text-align: left;}
.text-c { text-align: center; }
.center-v { display: flex; align-items: center;}
.align-center { align-items: center;}
.justify-center { justify-content: center;}

.bg-light { background: $light-gray; }
.bg-dark { background: $secondary-color; }
.text-green { color: $primary-color; }
.text-red { color: $brightcoral; }
.bold {font-weight: 600;}
.red {
  color: $attention-color;
}
.flex {
  display: flex;
  flex-wrap: wrap;
}
.flex-inline {
  display: inline-flex;
}
.row {
  display: flex;
  flex-direction: row;
}
.col {
  display: flex;
  flex-direction: column;
}
.center, .items-center {
  align-items: center;
}
.start {
  justify-content: flex-start;
}
.end {
  justify-content: flex-end;
}
.space {
  justify-content: space-between;
}

.border {
  border: 1px solid $border-color;
  border-radius: $border-radius;
}


//Connection requests
/* Connection requests container */
.requests-container {
  width: 100%;
  padding: 0;
}

/* Request list */
.request-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Individual request card */
.request-card {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  background-color: white;
}

.request-content {
  display: flex;
  gap: 1.5rem;
}

/* User avatar circle */
.request-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: #d7d7d7;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  flex-shrink: 0;
  &.project {
    background-color: $dark-brown;
  }
}

/* Request information section */
.request-info {
  flex-grow: 1;
}

.requester-name {
  font-weight: 500;
  font-size: 1.125rem;
  color: $blue;
  text-decoration: none;
  line-height: 1.5em;
  &a {
    color: $blue;
  }
  &:visited {
    color: $blue;
  }
}

.requester-name:hover {
  text-decoration: underline;
}

.requester-location {
  font-size: $small-font-size;
  margin-top: 0.25rem;
}

/* Message section */
.request-message {
  margin-top: 1rem;
  line-height: 1.5;
}

.message-expand {
  color: $blue;
  text-decoration: none;
  font-size: $small-font-size;
  margin-left: 0.5rem;
}

.message-expand:hover {
  text-decoration: underline;
}

/* Project section */


.action-button {
  font-size: $small-font-size;
  color: $blue;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: right;
}

.action-button:hover {
  text-decoration: underline;
}

.action-button--reject {
  color: #dc2626;
}

.requested-project {
  margin-top: 1rem;
  padding: 1rem;
  background-color: $linen;
  border-radius: 0.375rem;
}

.project-label {
  font-weight: 600;
  color: #374151;
  margin-right: 0.5rem;
}

/* Actions section */
.request-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.action-button--primary {
  background-color: $blue;
  color: white;
  border: 1px solid $blue;
}

.action-button--outline {
  background-color: white;
  color: $blue;
  border: 1px solid $blue;
}

.action-button--reject {
  background-color: $red;
  color: white;
  border: 1px solid $red;
}


//Projects list 
/* Projects filtering section */

.projects-filter {
  width: 100%;
  margin: 1rem 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  width: 200px;
}

.filter-button {
  padding: 0.5rem 1rem;
  background-color: $blue;
  color: white;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
}


/* Project list new ----------------------------*/
// Project list styling

.projects-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.project-item {
  width: 100%;
  display: flex;
  align-items:baseline; 
  padding: 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}


.project-date {
  width: 6rem;
  flex-shrink: 0;
  margin-left: 0.5rem;
  text-align: right;
}

.project-main {
  flex-grow: 1;
  margin-right: 0.5rem;
  min-width: 0;
  max-width:55%;
}

.project-title {
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 500;
  color: $blue;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0.25rem;
  a {
    text-decoration: none;
  }
  a:hover { 
    text-decoration: underline;
  }
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.project-location, .project-author, .project-sharing, .project-access {
  display: flex;
  align-items: center;
  line-height: 1;
  justify-content: center;
}

.icon-sm {
  width: 0.75rem;
  height: 0.75rem;
  margin-right: 0.25rem;
  flex-shrink: 0;
}

.author-avatar {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 0.25rem;
}

.project-category {
  display: flex;
  flex-direction: column;
  width: 10rem;
  flex-shrink: 0;
  margin-right: 0.5rem;
  align-items: flex-start; 
  gap: 0.25rem;
}

.project-actions {
  width: 11rem;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.action-link {
  color: $blue;
  text-decoration: none;
  text-shadow: 1px 1px 1px rgba(0,0,0,0.004);
}

.action-link:hover {
  text-decoration: underline;
}

.action-button {
  color: $blue;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-align: right;
}

.action-button:hover {
  text-decoration: underline;
}

.action-button--reject {
  color: #dc2626;
}

.no-projects {
  padding: 1.5rem;
  text-align: center;
}



@media (max-width: $breakpoint-mobile) {
  .project-item {
    flex-direction: column;
  }
  .project-main, .project-category, .project-actions, .project-date {
    width: 100%;
    max-width: 100%;
    margin: 0.15rem 0;
    gap: 0.30rem;
  }
  .project-category {
    flex-direction: row;
  }
}







/* Project card styles */

.project-card {
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;

  .project-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
    gap: 5px;
  }
  .project-location {
    margin-top: 0.5rem;
  }

}

.project-visibility {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.25rem;
}

.visibility-badge {
  //display: inline-flex;
  align-items: center;
  background-color: #f3f4f6;
  color: #4b5563;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: $small-font-size;
}

.project-content {
  margin: 1rem 0;
}

.project-timestamp {
  font-size: $small-font-size;
  margin: 1rem 0;
}

.character-count {
  margin: 10px;
}



/* Access status badges */
.access-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: $small-font-size;
}

.access-badge--pending {
  background-color: #fef3c7;
  color: #92400e;
}

.access-badge--full_details {
  background-color: #d1fae5;
  color: $green;
}

.access-badge--rejected {
  background-color: #fee2e2;
  color: $red;
}

/* Action buttons */
.action-link {
  color: $blue;
  text-decoration: none;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

.action-link:hover {
  text-decoration: underline;
}

.access-request-button {
  background: none;
  border: none;
  color: $blue;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
}

.access-request-button:hover {
  text-decoration: underline;
}




/* Projects - Authorization overview specific styles */
.my-projects {
  .side-right {
    flex: 1 1 45%;
    order: 1;

  }

  .main-content {
    flex: 1 1 55%;
    order: 2;
  }

  .project-card {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .sidelink {
    cursor: pointer
  }

  .active {
    border: 1px solid $primary-color
  }
}

auth-list {
  // border-top: 1px solid #e2e8f0;
  padding-top: 0rem;
}

.auth-users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 0.5rem;
}

.auth-user-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}

.auth-user-info {
  display: flex;
  width:100%;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
}

.auth-user-name {
  font-weight: 500;
  color: #374151;
}

.delete-auth-button {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .auth-users-grid {
    grid-template-columns: 1fr;
  }
}



// Modal window
.dynamic-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
}

.modal-content {
  background: white;
  max-width: 800px; // Make modal wider
  border: 1px solid #888;
  margin: 5% auto; // Adjust margin for better centering
  padding: 20px;
  width: 90%; // Responsive width
  position: relative;
  .card {
    width: 100%;
    min-height: 450px;
    background: white;
    border: none;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.hidden {
  display: none;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  color: $dark-gray;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-button:hover,
.close-button:focus {
  color: black;
  text-decoration: none;
}

.hidden {
  display: none;
}

.pagy.nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
  max-width: 1024px;
  margin: 20px auto;
  padding: 0 $spacing-unit;
  
  a {
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    color: inherit;
    text-decoration: none;
    
    &[aria-disabled="true"] {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.current {
      background-color: #F5F5F5;
      //color: white;
      border-color: #F5F5F5;
    }
  }
}

.language-dropdown {
  position: relative;
  margin: 0 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.language-dropdown__current {
  padding: 0.5rem;
  font-weight: 500;
  color: white;
  border: 1px solid transparent;
  border-radius: 4px;
}

.language-dropdown__current:hover {
  border-color: $border-color;
}

.language-dropdown__menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background-color: white;
  border: 1px solid $border-color;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1050;
  min-width: max-content;
  padding: 0.5rem 0;
}

.language-dropdown__menu.hidden {
  display: none;
}

.language-dropdown__item {
  display: block;
  padding: 0.5rem 1.5rem;
  color: $text-color;
  text-decoration: none;
  white-space: nowrap;
}

.language-dropdown__item:visited {
  color: $text-color;
}

.language-dropdown__item:hover {
  color: $primary-color;
  text-decoration: none;
} 

.cta-box-profile {
  display: flex;
  gap: 1rem;
  font-size: 1.25rem;
  font-weight: 500;
  background-color: $primary-color;
  color: white;
  padding: 1rem;
  border-radius: $border-radius;
  margin: 1rem 0;
}

// Secure Lightbox Styles (Chunk 7)
.secure-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.hidden {
    display: none;
  }
}

.lightbox-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.lightbox-header {
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.lightbox-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  
  &:hover {
    color: #374151;
  }
}

.lightbox-body {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
  
  iframe {
    width: 80vw;
    height: 70vh;
    border: none;
  }
}

.lightbox-loading {
  text-align: center;
  color: #6b7280;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 1rem;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.lightbox-footer {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
  
  button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    
    &:hover {
      background-color: #2563eb;
    }
  }
}

// File thumbnail styles 
.files-box {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  flex: 1 0 150px;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}
.file-thumbnail-container {
  display: flex;
  width: 200px;
  height: 100px;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
  
  // Mobile responsive
  @media (max-width: 768px) {
    width: 100%;
    height: 80px;
  }
}

.file-thumbnail {
  max-width: 300px;
  max-height: 200px;
  width: auto;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

.file-name, .file-meta {
  font-weight: 400;
  color: $text-color;
  margin-bottom: 4px;
  text-align: left;
}

.file-actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.download-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: $blue;
  text-decoration: none;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba($blue, 0.1);
    text-decoration: none;
  }
}

// Missing icon size class that was referenced in template
.icon-48 {
  width: 48px;
  height: 48px;
}

// Inline File Viewer for Chunk 2 implementation
.inline-file-viewer {
  margin-top: 2rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
}

.inline-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: $text-color;
  }
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.inline-viewer-content {
  padding: 1rem;
  text-align: center;
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
  }
  
  iframe {
    width: 100%;
    height: 600px;
    border: none;
    border-radius: 4px;
  }
}

.icon-24 {
  width: 24px;
  height: 24px;
}

.icon-16 {
  width: 16px;
  height: 16px;
}

// Viewer active state layout changes
.file-viewer-container {
  .side-right {
    display: none;
  }
}

.file-viewer-container.viewer-active {
  .file-grid {
    display: none;
  }
  
  #inline-file-viewer {
    display: block !important;
    margin-top: 0;
  }
  
  .side-right {
    display: block !important;
    min-width: 200px;
  }
  
  #sidebar-thumbnail-list {
    display: block;
  }
}

// Hide sidebar thumbnails by default
#sidebar-thumbnail-list {
  display: none;
}

// Sidebar thumbnail wrapper
.sidebar-thumbnail-wrapper {
  position: relative;
  width: 100%;
}

// Sidebar download overlay
.sidebar-download-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  text-align: center;
  color: #333;
}

// Sidebar thumbnail list
.sidebar-thumbnail-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 699px;
  overflow-y: auto;
  position: relative;
  
  .file-item {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    cursor: default; 
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f8f9fa;
    }
    
    &.active {
      background-color: #e7f3ff;
      border-color: $blue;
      box-shadow: 0 0 0 2px rgba($blue, 0.2);
    }
  }
  
  .file-thumbnail-container {
    width: 160px;
    height: 100px;
    margin-bottom: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    // Mobile responsive
    @media (max-width: 768px) {
      width: 100%;
      height: 80px;
    }
    background-color: #e8e8e8;
    border-radius: 8px;
  }
  
  .file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 8px;
  }
  
  .file-name {
    font-size: 12px;
    margin-bottom: 2px;
  }
  
  .file-meta {
    font-size: 11px;
    color: #6c757d;
  }
  
  // Download link in sidebar
  .sidebar-download-link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: $blue;
    text-decoration: none;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 5px;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: rgba($blue, 0.1);
      text-decoration: none;
    }
  }
}

.text-link.red {
  color: $brightcoral;
}

.checkbox-inline {
  display: flex;
  align-items: center;
  gap: 5px;
  label {
    margin: 0;
  }
}
.devise-links {
  margin-top: 20px;
  line-height: 1.4em;
}

@media (max-width: 768px) {
  .form .actions {
    margin-bottom: 20px;
  }

}

// Responsive adjustments for the modal
@media screen and (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

.file-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.file-list-item .file-checkbox {
  flex-shrink: 0;
  margin-right: 15px;
}

.file-list-item .file-select-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.file-list-item .file-info {
  flex-grow: 1;
  margin-right: 15px;
}

.delete-file-button, .cleanup-stuck-btn {
  @extend .button; 
  background-color: $attention-color;
  color: white;
  padding: 5px 10px; 
  font-weight: 500;
  font-size: 13px;
}