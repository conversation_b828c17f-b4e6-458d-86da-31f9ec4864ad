// app/assets/stylesheets/components/_connections.scss
@import "variables";

.connections-container {
  max-width: 64rem;
  margin: 10px auto;
  //padding: 1rem;
}

.connections-search {
  margin-bottom: 1.5rem;

  .search-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-wrap: wrap;
    }
    
  }

  .filter-select {
    width: 12rem;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: white;
  }

  .search-input {
    padding: 0.5rem;
    padding-right: 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
  }

  .search-button {
    // right: 0.75rem;
    background: $blue;

    display: inline-block;
    padding: 12px 24px;
    border: none;
    color: white;
    font-family: "Inter", sans-serif;
    font-size: 16px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
  }
}

.connections-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.connection-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  align-items: flex-start;
}

.profile-avatar {
  flex-shrink: 0;

  .avatar-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: #d7d7d7;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
  }
}

.connection-content {
  flex-grow: 1;
  min-width: 0;
}

.connection-header {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.connection-main-info {
  .connection-name {
    font-weight: 600;
    font-size: 1.125rem;
    
    a {
      color: $blue;
      text-decoration: none;
      
      &:hover {
        color: $blue;
        text-decoration: underline;
      }
    }
  }

  .connection-location, .connection-email {
    font-size: $small-font-size;
    color: $text-color;
  }
}

.connection-bio {
  font-size: $small-font-size;
  color: $text-color;
  line-height: 1.5;
  
  .bio-expand {
    color: $blue;
    text-decoration: none;
    margin-left: 0.5rem;
    font-size: $small-font-size;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.connection-actions {
  flex-shrink: 0;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: $small-font-size;
  
  &--self {
    background-color: #f1f5f9;
    color: $text-color;
  }
  
  &--connected {
    background-color: $primary-color;
    color: white;
  }
  &--pending {
    background-color: #fef3c7;
    color: #92400e;
  }
}

.profile-warning {
  font-size: $small-font-size;
  color: $red;
  margin-bottom: 0.5rem;
}

.profile-edit-button {
  display: inline-block;
  padding: 0.25rem 1rem;
  background-color: $blue;
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: $small-font-size;
  
  &:hover {
    background-color: $blue;
  }
}

.action-button {
  display: inline-block;
  padding: 0.25rem 1rem;
  text-decoration: none;
  border-radius: 0.5rem;
  font-size: $small-font-size;
  
  &--outline {
    border: 1px solid $blue;
    color: $blue;
  }
}

.empty-state {
  text-align: center;
  color: $text-color;
  padding: 2rem;
}